<?php
/**
 * African Business Examples Manager for ChatGABI
 * Manages country-specific business case studies and success stories
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * African Business Examples Manager Class
 */
class ChatGABI_African_Examples_Manager {

    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'chatgabi_african_examples';
    }

    /**
     * Create African examples database table
     */
    public function create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            company_name varchar(255) NOT NULL,
            country varchar(2) NOT NULL,
            industry varchar(100) NOT NULL,
            business_type enum('startup', 'sme', 'enterprise') DEFAULT 'sme',
            success_story longtext NOT NULL,
            metrics text,
            year int(4) NOT NULL,
            revenue_range varchar(50),
            employee_count varchar(50),
            funding_stage varchar(100),
            key_achievements text,
            challenges_overcome text,
            lessons_learned text,
            contact_info text,
            website_url varchar(255),
            logo_url varchar(255),
            is_featured tinyint(1) DEFAULT 0,
            is_verified tinyint(1) DEFAULT 0,
            status enum('active', 'inactive', 'pending') DEFAULT 'active',
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY country_industry (country, industry),
            KEY business_type (business_type),
            KEY featured_verified (is_featured, is_verified, status),
            KEY year_country (year, country),
            KEY created_by (created_by)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        return $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") === $this->table_name;
    }

    /**
     * Get examples by country and industry
     */
    public function get_examples($country = '', $industry = '', $business_type = '', $limit = 10) {
        global $wpdb;
        
        $where_conditions = array("status = 'active'");
        $where_values = array();
        
        if (!empty($country)) {
            $where_conditions[] = "country = %s";
            $where_values[] = $country;
        }
        
        if (!empty($industry)) {
            $where_conditions[] = "industry = %s";
            $where_values[] = $industry;
        }
        
        if (!empty($business_type)) {
            $where_conditions[] = "business_type = %s";
            $where_values[] = $business_type;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT * FROM {$this->table_name} 
                WHERE {$where_clause} 
                ORDER BY is_featured DESC, is_verified DESC, year DESC 
                LIMIT %d";
        
        $where_values[] = $limit;
        
        return $wpdb->get_results($wpdb->prepare($sql, $where_values));
    }

    /**
     * Get featured examples for homepage/template integration
     */
    public function get_featured_examples($limit = 5) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_name} 
             WHERE status = 'active' AND is_featured = 1 AND is_verified = 1
             ORDER BY year DESC, company_name ASC 
             LIMIT %d",
            $limit
        ));
    }

    /**
     * Add new business example
     */
    public function add_example($data) {
        global $wpdb;
        
        $defaults = array(
            'business_type' => 'sme',
            'is_featured' => 0,
            'is_verified' => 0,
            'status' => 'pending',
            'created_by' => get_current_user_id()
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Sanitize data
        $sanitized_data = array(
            'company_name' => sanitize_text_field($data['company_name']),
            'country' => sanitize_text_field($data['country']),
            'industry' => sanitize_text_field($data['industry']),
            'business_type' => sanitize_text_field($data['business_type']),
            'success_story' => wp_kses_post($data['success_story']),
            'metrics' => sanitize_textarea_field($data['metrics']),
            'year' => intval($data['year']),
            'revenue_range' => sanitize_text_field($data['revenue_range']),
            'employee_count' => sanitize_text_field($data['employee_count']),
            'funding_stage' => sanitize_text_field($data['funding_stage']),
            'key_achievements' => sanitize_textarea_field($data['key_achievements']),
            'challenges_overcome' => sanitize_textarea_field($data['challenges_overcome']),
            'lessons_learned' => sanitize_textarea_field($data['lessons_learned']),
            'contact_info' => sanitize_textarea_field($data['contact_info']),
            'website_url' => esc_url_raw($data['website_url']),
            'logo_url' => esc_url_raw($data['logo_url']),
            'is_featured' => intval($data['is_featured']),
            'is_verified' => intval($data['is_verified']),
            'status' => sanitize_text_field($data['status']),
            'created_by' => intval($data['created_by'])
        );
        
        $result = $wpdb->insert($this->table_name, $sanitized_data);
        
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert business example');
        }
        
        return $wpdb->insert_id;
    }

    /**
     * Update business example
     */
    public function update_example($id, $data) {
        global $wpdb;
        
        // Remove ID and timestamps from update data
        unset($data['id'], $data['created_at'], $data['updated_at']);
        
        // Sanitize data
        foreach ($data as $key => $value) {
            switch ($key) {
                case 'success_story':
                    $data[$key] = wp_kses_post($value);
                    break;
                case 'website_url':
                case 'logo_url':
                    $data[$key] = esc_url_raw($value);
                    break;
                case 'metrics':
                case 'key_achievements':
                case 'challenges_overcome':
                case 'lessons_learned':
                case 'contact_info':
                    $data[$key] = sanitize_textarea_field($value);
                    break;
                case 'year':
                case 'is_featured':
                case 'is_verified':
                case 'created_by':
                    $data[$key] = intval($value);
                    break;
                default:
                    $data[$key] = sanitize_text_field($value);
            }
        }
        
        $result = $wpdb->update(
            $this->table_name,
            $data,
            array('id' => $id),
            null,
            array('%d')
        );
        
        return $result !== false;
    }

    /**
     * Delete business example
     */
    public function delete_example($id) {
        global $wpdb;
        
        return $wpdb->delete(
            $this->table_name,
            array('id' => $id),
            array('%d')
        ) !== false;
    }

    /**
     * Get examples for template integration
     */
    public function get_examples_for_template($country, $industry, $business_type = '') {
        $examples = $this->get_examples($country, $industry, $business_type, 3);
        
        if (empty($examples)) {
            // Fallback to country-only examples
            $examples = $this->get_examples($country, '', $business_type, 3);
        }
        
        return $examples;
    }

    /**
     * Get statistics for admin dashboard
     */
    public function get_statistics() {
        global $wpdb;
        
        $stats = array();
        
        // Total examples by country
        $country_stats = $wpdb->get_results(
            "SELECT country, COUNT(*) as count 
             FROM {$this->table_name} 
             WHERE status = 'active' 
             GROUP BY country 
             ORDER BY count DESC"
        );
        
        $stats['by_country'] = $country_stats;
        
        // Total examples by industry
        $industry_stats = $wpdb->get_results(
            "SELECT industry, COUNT(*) as count 
             FROM {$this->table_name} 
             WHERE status = 'active' 
             GROUP BY industry 
             ORDER BY count DESC 
             LIMIT 10"
        );
        
        $stats['by_industry'] = $industry_stats;
        
        // Overall totals
        $stats['total'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'active'"
        );
        
        $stats['featured'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'active' AND is_featured = 1"
        );
        
        $stats['verified'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'active' AND is_verified = 1"
        );
        
        return $stats;
    }

    /**
     * Initialize with sample data
     */
    public function initialize_sample_data() {
        // Check if data already exists
        global $wpdb;
        $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");

        if ($existing_count > 0) {
            return false; // Data already exists
        }

        $sample_examples = $this->get_sample_examples();

        foreach ($sample_examples as $example) {
            $this->add_example($example);
        }

        return true;
    }

    /**
     * Get sample business examples for initialization
     */
    private function get_sample_examples() {
        return array(
            // Ghana Examples
            array(
                'company_name' => 'Farmerline',
                'country' => 'GH',
                'industry' => 'Agriculture Technology',
                'business_type' => 'startup',
                'success_story' => 'Farmerline provides agricultural services to smallholder farmers through mobile technology. Founded in 2013, they have reached over 1 million farmers across Ghana and other African countries.',
                'metrics' => '1M+ farmers reached, $4.5M funding raised, 15+ countries served',
                'year' => 2023,
                'revenue_range' => '$1M - $5M',
                'employee_count' => '50-100',
                'funding_stage' => 'Series A',
                'key_achievements' => 'Winner of multiple international awards, partnerships with major telcos',
                'challenges_overcome' => 'Rural connectivity issues, farmer education, payment collection',
                'lessons_learned' => 'Mobile-first approach essential for African markets, local partnerships crucial',
                'website_url' => 'https://farmerline.co',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            array(
                'company_name' => 'Niche Cocoa',
                'country' => 'GH',
                'industry' => 'Agriculture',
                'business_type' => 'sme',
                'success_story' => 'Premium cocoa processing company that works directly with farmers to produce high-quality chocolate products for export markets.',
                'metrics' => '500+ farmers in network, 20% premium pricing, 15 countries export',
                'year' => 2023,
                'revenue_range' => '$500K - $1M',
                'employee_count' => '20-50',
                'funding_stage' => 'Self-funded',
                'key_achievements' => 'Fair trade certification, direct farmer relationships',
                'challenges_overcome' => 'Quality control, international certification, market access',
                'lessons_learned' => 'Direct farmer relationships improve quality and sustainability',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            // Kenya Examples
            array(
                'company_name' => 'Twiga Foods',
                'country' => 'KE',
                'industry' => 'Food Technology',
                'business_type' => 'startup',
                'success_story' => 'B2B marketplace connecting farmers to vendors through mobile technology and logistics. Revolutionizing food distribution in Kenya.',
                'metrics' => '17,000+ vendors, 13,000+ farmers, $30M+ funding',
                'year' => 2023,
                'revenue_range' => '$10M+',
                'employee_count' => '200+',
                'funding_stage' => 'Series B',
                'key_achievements' => 'Market leader in food distribution, strong unit economics',
                'challenges_overcome' => 'Cold chain logistics, payment systems, farmer onboarding',
                'lessons_learned' => 'Technology must solve real distribution problems, focus on unit economics',
                'website_url' => 'https://twiga.ke',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            array(
                'company_name' => 'iCow',
                'country' => 'KE',
                'industry' => 'Agriculture Technology',
                'business_type' => 'startup',
                'success_story' => 'SMS-based platform providing dairy farmers with information on animal husbandry, breeding, and farm management.',
                'metrics' => '500,000+ farmers reached, 70% improvement in milk production',
                'year' => 2023,
                'revenue_range' => '$100K - $500K',
                'employee_count' => '10-20',
                'funding_stage' => 'Seed',
                'key_achievements' => 'Significant impact on farmer productivity, scalable SMS model',
                'challenges_overcome' => 'Rural connectivity, farmer literacy, monetization',
                'lessons_learned' => 'Simple technology can have massive impact, focus on farmer outcomes',
                'website_url' => 'https://icow.co.ke',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            // Nigeria Examples
            array(
                'company_name' => 'Paystack',
                'country' => 'NG',
                'industry' => 'Financial Technology',
                'business_type' => 'startup',
                'success_story' => 'Payment processing platform that became the leading fintech in Nigeria before being acquired by Stripe for $200M.',
                'metrics' => '$200M acquisition, 60,000+ businesses, millions of transactions',
                'year' => 2020,
                'revenue_range' => '$10M+',
                'employee_count' => '100+',
                'funding_stage' => 'Acquired',
                'key_achievements' => 'Largest fintech acquisition in Africa, market leadership',
                'challenges_overcome' => 'Regulatory compliance, payment infrastructure, fraud prevention',
                'lessons_learned' => 'Focus on developer experience, solve real payment problems',
                'website_url' => 'https://paystack.com',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            array(
                'company_name' => 'Farmcrowdy',
                'country' => 'NG',
                'industry' => 'Agriculture Technology',
                'business_type' => 'startup',
                'success_story' => 'Digital agriculture platform connecting smallholder farmers with sponsors and providing farm management services.',
                'metrics' => '15,000+ farmers, $1M+ funding, multiple states coverage',
                'year' => 2023,
                'revenue_range' => '$500K - $1M',
                'employee_count' => '50-100',
                'funding_stage' => 'Series A',
                'key_achievements' => 'Innovative crowdfunding model, strong farmer network',
                'challenges_overcome' => 'Trust building, logistics, weather risks',
                'lessons_learned' => 'Community-driven approach works, technology must be accessible',
                'website_url' => 'https://farmcrowdy.com',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            // South Africa Examples
            array(
                'company_name' => 'Yoco',
                'country' => 'ZA',
                'industry' => 'Financial Technology',
                'business_type' => 'startup',
                'success_story' => 'Point-of-sale and business management platform for small businesses, processing billions in transactions.',
                'metrics' => '150,000+ merchants, $83M funding, billions in transactions',
                'year' => 2023,
                'revenue_range' => '$10M+',
                'employee_count' => '200+',
                'funding_stage' => 'Series B',
                'key_achievements' => 'Market leader in SME payments, strong growth metrics',
                'challenges_overcome' => 'Hardware distribution, merchant education, competition',
                'lessons_learned' => 'Focus on SME needs, provide complete business solutions',
                'website_url' => 'https://yoco.co.za',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            ),
            array(
                'company_name' => 'Aerobotics',
                'country' => 'ZA',
                'industry' => 'Agriculture Technology',
                'business_type' => 'startup',
                'success_story' => 'AI-powered crop monitoring using drones and satellite imagery for precision agriculture.',
                'metrics' => '3,000+ farms monitored, 18 countries, $17M funding',
                'year' => 2023,
                'revenue_range' => '$5M - $10M',
                'employee_count' => '50-100',
                'funding_stage' => 'Series A',
                'key_achievements' => 'Global expansion, proven ROI for farmers',
                'challenges_overcome' => 'Technology adoption, regulatory approvals, scaling',
                'lessons_learned' => 'Prove ROI early, focus on farmer outcomes, global thinking',
                'website_url' => 'https://aerobotics.com',
                'is_featured' => 1,
                'is_verified' => 1,
                'status' => 'active'
            )
        );
    }
}

/**
 * Get African Examples Manager instance
 */
function chatgabi_get_african_examples_manager() {
    static $instance = null;

    if ($instance === null) {
        $instance = new ChatGABI_African_Examples_Manager();
    }

    return $instance;
}
